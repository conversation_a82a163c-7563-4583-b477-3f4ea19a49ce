"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/page",{

/***/ "(app-pages-browser)/./src/app/warranties/page.tsx":
/*!*************************************!*\
  !*** ./src/app/warranties/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WarrantiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n/**\n * Warranty Management Main Page\n *\n * This page provides an overview of warranty management with quick access\n * to different warranty categories and status information.\n */ function WarrantiesPage() {\n    _s();\n    _s1();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        active: 0,\n        expiring: 0,\n        expired: 0,\n        pending: 0,\n        total: 0\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load warranty statistics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WarrantiesPage.useEffect\": ()=>{\n            const loadStats = {\n                \"WarrantiesPage.useEffect.loadStats\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const [warrantyRes, expiringRes] = await Promise.all([\n                            fetch('/api/warranties?take=1000', {\n                                credentials: 'include'\n                            }),\n                            fetch('/api/warranties/expiring?days=30', {\n                                credentials: 'include'\n                            })\n                        ]);\n                        if (!warrantyRes.ok) {\n                            console.error('Warranty API error:', warrantyRes.status, warrantyRes.statusText);\n                            if (warrantyRes.status === 401) {\n                                throw new Error('Authentication required. Please log in.');\n                            }\n                            throw new Error(\"Failed to fetch warranties: \".concat(warrantyRes.statusText));\n                        }\n                        if (!expiringRes.ok) {\n                            console.error('Expiring warranties API error:', expiringRes.status, expiringRes.statusText);\n                            if (expiringRes.status === 401) {\n                                throw new Error('Authentication required. Please log in.');\n                            }\n                            throw new Error(\"Failed to fetch expiring warranties: \".concat(expiringRes.statusText));\n                        }\n                        const [warrantyData, expiringData] = await Promise.all([\n                            warrantyRes.json(),\n                            expiringRes.json()\n                        ]);\n                        const warranties = warrantyData.warranties || [];\n                        const expiringWarranties = expiringData.warranties || [];\n                        const newStats = {\n                            total: warranties.length,\n                            active: warranties.filter({\n                                \"WarrantiesPage.useEffect.loadStats\": (w)=>w.status === 'ACTIVE'\n                            }[\"WarrantiesPage.useEffect.loadStats\"]).length,\n                            expired: warranties.filter({\n                                \"WarrantiesPage.useEffect.loadStats\": (w)=>w.status === 'EXPIRED'\n                            }[\"WarrantiesPage.useEffect.loadStats\"]).length,\n                            pending: warranties.filter({\n                                \"WarrantiesPage.useEffect.loadStats\": (w)=>w.status === 'PENDING'\n                            }[\"WarrantiesPage.useEffect.loadStats\"]).length,\n                            expiring: expiringWarranties.length\n                        };\n                        setStats(newStats);\n                        setError(null);\n                    } catch (err) {\n                        console.error('Error loading warranty statistics:', err);\n                        setError('Failed to load warranty statistics');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"WarrantiesPage.useEffect.loadStats\"];\n            loadStats();\n        }\n    }[\"WarrantiesPage.useEffect\"], []);\n    const handleTabChange = (value)=>{\n        setActiveTab(value);\n    };\n    const handleExport = async ()=>{\n        try {\n            const response = await fetch('/api/warranties/export?format=CSV', {\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to export warranties');\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"all-warranties-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Error exporting warranties:', error);\n            setError('Failed to export warranties');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Warranty Management\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Manage in-warranty and out-of-warranty products, track components, and monitor warranty status\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"secondary\",\n                                    onClick: handleExport,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"secondary\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/warranties/status\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Status Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                        defaultValue: \"overview\",\n                        value: activeTab,\n                        onValueChange: handleTabChange,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"overview\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"in-warranty\",\n                                        children: \"In-Warranty\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"out-warranty\",\n                                        children: \"Out-of-Warranty\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"components\",\n                                        children: \"Components\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"alerts\",\n                                        children: \"Alerts\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"overview\",\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Active Warranties\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: isLoading ? '...' : stats.active\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Expiring Soon\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: isLoading ? '...' : stats.expiring\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Expired\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: isLoading ? '...' : stats.expired\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Pending\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: isLoading ? '...' : stats.pending\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-red-200 bg-red-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"pt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"In-Warranty Management\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Manage products currently under warranty coverage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/in-warranty\",\n                                                                children: \"View In-Warranty Products\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Out-of-Warranty Management\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Manage products that are no longer under warranty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/out-warranty\",\n                                                                children: \"View Out-of-Warranty Products\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Component Tracking\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Track individual components and their warranty status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/components\",\n                                                                children: \"View Component Tracking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Warranty Status Dashboard\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Monitor warranty status and expiration alerts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/status\",\n                                                                children: \"View Status Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"BLUESTAR Warranties\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Manage vendor-specific BLUESTAR warranty workflows\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/bluestar\",\n                                                                children: \"View BLUESTAR Warranties\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Warranty Alerts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"View and manage warranty expiration notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/alerts\",\n                                                                children: \"View Warranty Alerts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"in-warranty\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"In-Warranty Management\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"This section will display in-warranty products and management tools.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/warranties/in-warranty\",\n                                                children: \"Go to In-Warranty Management\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"out-warranty\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"Out-of-Warranty Management\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"This section will display out-of-warranty products and management tools.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/warranties/out-warranty\",\n                                                children: \"Go to Out-of-Warranty Management\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"components\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"Component Tracking\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"This section will display component warranty tracking tools.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/warranties/components\",\n                                                children: \"Go to Component Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"alerts\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"Warranty Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"This section will display warranty expiration alerts and notifications.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/warranties/alerts\",\n                                                children: \"Go to Warranty Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n        lineNumber: 102,\n        columnNumber: 10\n    }, this);\n}\n_s(WarrantiesPage, \"LCb8EBKFfo12nkIfeoi0ldqfTao=\");\n_c1 = WarrantiesPage;\n_s1(WarrantiesPage, \"LCb8EBKFfo12nkIfeoi0ldqfTao=\");\n_c = WarrantiesPage;\nvar _c;\n$RefreshReg$(_c, \"WarrantiesPage\");\nvar _c1;\n$RefreshReg$(_c1, \"WarrantiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/page.tsx\n"));

/***/ })

});