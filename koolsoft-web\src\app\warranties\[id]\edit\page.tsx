'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Save, 
  X, 
  AlertTriangle,
  Calendar,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';


interface Customer {
  id: string;
  name: string;
  city?: string;
}

interface Executive {
  id: string;
  name: string;
  email?: string;
}

interface Warranty {
  id: string;
  customerId: string;
  executiveId?: string;
  contactPersonId?: string;
  bslNo: string;
  bslDate: string;
  bslAmount: number;
  frequency: number;
  numberOfMachines: number;
  installDate: string;
  warrantyDate: string;
  warningDate: string;
  technicianId?: string;
  amcId?: string;
  status: string;
}

export default function EditWarrantyPage() {
  const params = useParams();
  const router = useRouter();
  const warrantyId = params.id as string;
  
  const [formData, setFormData] = useState<Warranty>({
    id: '',
    customerId: '',
    executiveId: '',
    contactPersonId: '',
    bslNo: '',
    bslDate: '',
    bslAmount: 0,
    frequency: 0,
    numberOfMachines: 1,
    installDate: '',
    warrantyDate: '',
    warningDate: '',
    technicianId: '',
    amcId: '',
    status: 'ACTIVE'
  });
  
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [executives, setExecutives] = useState<Executive[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Load warranty data and form options
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        
        const [warrantyRes, customersRes, executivesRes] = await Promise.all([
          fetch(`/api/warranties/${warrantyId}`, { credentials: 'include' }),
          fetch('/api/customers?take=1000', { credentials: 'include' }),
          fetch('/api/users?role=EXECUTIVE&take=1000', { credentials: 'include' })
        ]);

        if (!warrantyRes.ok) {
          throw new Error('Failed to load warranty data');
        }

        const warrantyData = await warrantyRes.json();
        
        // Convert dates to YYYY-MM-DD format for input fields
        const formattedData = {
          ...warrantyData,
          bslDate: warrantyData.bslDate ? new Date(warrantyData.bslDate).toISOString().split('T')[0] : '',
          installDate: warrantyData.installDate ? new Date(warrantyData.installDate).toISOString().split('T')[0] : '',
          warrantyDate: warrantyData.warrantyDate ? new Date(warrantyData.warrantyDate).toISOString().split('T')[0] : '',
          warningDate: warrantyData.warningDate ? new Date(warrantyData.warningDate).toISOString().split('T')[0] : '',
          executiveId: warrantyData.executiveId || '',
          contactPersonId: warrantyData.contactPersonId || '',
          technicianId: warrantyData.technicianId || '',
          amcId: warrantyData.amcId || ''
        };
        
        setFormData(formattedData);

        if (customersRes.ok) {
          const customersData = await customersRes.json();
          setCustomers(customersData.customers || []);
        }

        if (executivesRes.ok) {
          const executivesData = await executivesRes.json();
          setExecutives(executivesData.users || []);
        }
      } catch (error: any) {
        console.error('Error loading data:', error);
        setErrors({ submit: error.message || 'Failed to load warranty data' });
      } finally {
        setIsLoading(false);
        setIsLoadingData(false);
      }
    };

    if (warrantyId) {
      loadData();
    }
  }, [warrantyId]);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerId) {
      newErrors.customerId = 'Customer is required';
    }

    if (!formData.bslNo) {
      newErrors.bslNo = 'BSL Number is required';
    }

    if (!formData.numberOfMachines || formData.numberOfMachines < 1) {
      newErrors.numberOfMachines = 'Number of machines must be at least 1';
    }

    if (formData.installDate && formData.warrantyDate) {
      const installDate = new Date(formData.installDate);
      const warrantyDate = new Date(formData.warrantyDate);
      if (warrantyDate <= installDate) {
        newErrors.warrantyDate = 'Warranty date must be after install date';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare warranty data for API
      const warrantyData = {
        customerId: formData.customerId,
        executiveId: formData.executiveId || null,
        contactPersonId: formData.contactPersonId || null,
        bslNo: formData.bslNo,
        bslDate: formData.bslDate ? new Date(formData.bslDate).toISOString() : null,
        bslAmount: Number(formData.bslAmount),
        frequency: Number(formData.frequency),
        numberOfMachines: Number(formData.numberOfMachines),
        installDate: formData.installDate ? new Date(formData.installDate).toISOString() : null,
        warrantyDate: formData.warrantyDate ? new Date(formData.warrantyDate).toISOString() : null,
        warningDate: formData.warningDate ? new Date(formData.warningDate).toISOString() : null,
        technicianId: formData.technicianId || null,
        amcId: formData.amcId || null,
        status: formData.status
      };

      const response = await fetch(`/api/warranties/${warrantyId}`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(warrantyData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update warranty');
      }

      // Redirect to warranty detail page
      router.push(`/warranties/${warrantyId}`);
    } catch (error: any) {
      console.error('Error updating warranty:', error);
      setErrors({ submit: error.message || 'Failed to update warranty. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Edit Warranty</span>
                </CardTitle>
                <CardDescription className="text-gray-100">
                  Update warranty information and settings
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                <Button variant="secondary" asChild>
                  <Link href={`/warranties/${warrantyId}`}>
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Link>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="customerId" className="text-black">Customer *</Label>
                  <Select value={formData.customerId} onValueChange={(value) => handleInputChange('customerId', value)}>
                    <SelectTrigger id="customerId">
                      <SelectValue placeholder={isLoadingData ? "Loading customers..." : "Select customer"} />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name} {customer.city && `(${customer.city})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.customerId && (
                    <p className="text-sm text-red-600">{errors.customerId}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="executiveId" className="text-black">Executive</Label>
                  <Select value={formData.executiveId} onValueChange={(value) => handleInputChange('executiveId', value)}>
                    <SelectTrigger id="executiveId">
                      <SelectValue placeholder={isLoadingData ? "Loading executives..." : "Select executive"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No Executive</SelectItem>
                      {executives.map((executive) => (
                        <SelectItem key={executive.id} value={executive.id}>
                          {executive.name} {executive.email && `(${executive.email})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bslNo" className="text-black">BSL Number *</Label>
                  <Input
                    id="bslNo"
                    value={formData.bslNo}
                    onChange={(e) => handleInputChange('bslNo', e.target.value)}
                    placeholder="Enter BSL number"
                  />
                  {errors.bslNo && (
                    <p className="text-sm text-red-600">{errors.bslNo}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bslAmount" className="text-black">BSL Amount</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="bslAmount"
                      type="number"
                      value={formData.bslAmount}
                      onChange={(e) => handleInputChange('bslAmount', parseFloat(e.target.value) || 0)}
                      placeholder="0"
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="numberOfMachines" className="text-black">Number of Machines *</Label>
                  <Input
                    id="numberOfMachines"
                    type="number"
                    min="1"
                    value={formData.numberOfMachines}
                    onChange={(e) => handleInputChange('numberOfMachines', parseInt(e.target.value) || 1)}
                    placeholder="1"
                  />
                  {errors.numberOfMachines && (
                    <p className="text-sm text-red-600">{errors.numberOfMachines}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status" className="text-black">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                      <SelectItem value="EXPIRED">Expired</SelectItem>
                      <SelectItem value="CANCELLED">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bslDate" className="text-black">BSL Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="bslDate"
                      type="date"
                      value={formData.bslDate}
                      onChange={(e) => handleInputChange('bslDate', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="installDate" className="text-black">Install Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="installDate"
                      type="date"
                      value={formData.installDate}
                      onChange={(e) => handleInputChange('installDate', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="warrantyDate" className="text-black">Warranty Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="warrantyDate"
                      type="date"
                      value={formData.warrantyDate}
                      onChange={(e) => handleInputChange('warrantyDate', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  {errors.warrantyDate && (
                    <p className="text-sm text-red-600">{errors.warrantyDate}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="warningDate" className="text-black">Warning Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="warningDate"
                      type="date"
                      value={formData.warningDate}
                      onChange={(e) => handleInputChange('warningDate', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="frequency" className="text-black">Frequency</Label>
                  <Input
                    id="frequency"
                    type="number"
                    value={formData.frequency}
                    onChange={(e) => handleInputChange('frequency', parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>
              </div>

              {/* Error Display */}
              {errors.submit && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-black">{errors.submit}</AlertDescription>
                </Alert>
              )}

              {/* Form Actions */}
              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" asChild>
                  <Link href={`/warranties/${warrantyId}`}>
                    Cancel
                  </Link>
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Update Warranty
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
    </div>
  );
}
