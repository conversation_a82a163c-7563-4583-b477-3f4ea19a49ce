"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/status/page",{

/***/ "(app-pages-browser)/./src/app/warranties/status/page.tsx":
/*!********************************************!*\
  !*** ./src/app/warranties/status/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WarrantyStatusPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n// import { Progress } from '@/components/ui/progress'; // TODO: Create Progress component\n\n\n\n\n/**\n * Warranty Status Dashboard Page\n * \n * This page provides a comprehensive overview of warranty status across\n * all products, with visual indicators and key metrics.\n */ function WarrantyStatusPage() {\n    _s();\n    _s1();\n    const [statusData, setStatusData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock data for development\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WarrantyStatusPage.useEffect\": ()=>{\n            loadStatusData();\n        }\n    }[\"WarrantyStatusPage.useEffect\"], []);\n    const loadStatusData = async ()=>{\n        try {\n            setIsLoading(true);\n            // Fetch warranty summary data\n            const [warrantyRes, expiringRes, componentRes] = await Promise.all([\n                fetch('/api/warranties?take=1000', {\n                    credentials: 'include'\n                }),\n                fetch('/api/warranties/expiring?days=30', {\n                    credentials: 'include'\n                }),\n                fetch('/api/warranties/components?take=1000', {\n                    credentials: 'include'\n                })\n            ]);\n            if (!warrantyRes.ok) {\n                console.error('Warranty API error:', warrantyRes.status, warrantyRes.statusText);\n                if (warrantyRes.status === 401) {\n                    throw new Error('Authentication required. Please log in.');\n                }\n                throw new Error(\"Failed to fetch warranties: \".concat(warrantyRes.statusText));\n            }\n            if (!expiringRes.ok) {\n                console.error('Expiring warranties API error:', expiringRes.status, expiringRes.statusText);\n                if (expiringRes.status === 401) {\n                    throw new Error('Authentication required. Please log in.');\n                }\n                throw new Error(\"Failed to fetch expiring warranties: \".concat(expiringRes.statusText));\n            }\n            if (!componentRes.ok) {\n                console.error('Components API error:', componentRes.status, componentRes.statusText);\n                if (componentRes.status === 401) {\n                    throw new Error('Authentication required. Please log in.');\n                }\n                throw new Error(\"Failed to fetch components: \".concat(componentRes.statusText));\n            }\n            const [warrantyData, expiringData, componentData] = await Promise.all([\n                warrantyRes.json(),\n                expiringRes.json(),\n                componentRes.json()\n            ]);\n            // Calculate summary statistics\n            const warranties = warrantyData.warranties || [];\n            const expiringWarranties = expiringData.warranties || [];\n            const components = componentData.components || [];\n            const summary = {\n                total: warranties.length,\n                active: warranties.filter((w)=>w.status === 'ACTIVE').length,\n                expiring: expiringWarranties.length,\n                expired: warranties.filter((w)=>w.status === 'EXPIRED').length,\n                pending: warranties.filter((w)=>w.status === 'PENDING').length\n            };\n            // Calculate component status\n            const today = new Date();\n            const componentStatus = {\n                total: components.length,\n                active: components.filter((c)=>{\n                    const warrantyDate = c.warrantyDate ? new Date(c.warrantyDate) : null;\n                    return warrantyDate && warrantyDate > today;\n                }).length,\n                expiring: components.filter((c)=>{\n                    const warrantyDate = c.warrantyDate ? new Date(c.warrantyDate) : null;\n                    if (!warrantyDate) return false;\n                    const daysUntil = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                    return daysUntil > 0 && daysUntil <= 30;\n                }).length,\n                expired: components.filter((c)=>{\n                    const warrantyDate = c.warrantyDate ? new Date(c.warrantyDate) : null;\n                    return warrantyDate && warrantyDate <= today;\n                }).length\n            };\n            // Calculate expiring breakdown\n            const expiringBreakdown = {\n                next7Days: expiringWarranties.filter((w)=>{\n                    const warrantyDate = new Date(w.warrantyDate);\n                    const daysUntil = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                    return daysUntil <= 7;\n                }).length,\n                next30Days: expiringWarranties.length,\n                next90Days: warranties.filter((w)=>{\n                    if (!w.warrantyDate) return false;\n                    const warrantyDate = new Date(w.warrantyDate);\n                    const daysUntil = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                    return daysUntil > 0 && daysUntil <= 90;\n                }).length\n            };\n            setStatusData({\n                summary,\n                trends: {\n                    activeChange: 0,\n                    // Would need historical data to calculate\n                    expiringChange: 0,\n                    expiredChange: 0\n                },\n                expiringBreakdown,\n                componentStatus,\n                recentAlerts: [] // Would come from a separate alerts API\n            });\n            setError(null);\n        } catch (err) {\n            console.error('Error loading status data:', err);\n            setError('Failed to load warranty status data');\n            setStatusData(null);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        await loadStatusData();\n        setIsRefreshing(false);\n    };\n    const getStatusPercentage = (count, total)=>{\n        return total > 0 ? Math.round(count / total * 100) : 0;\n    };\n    const getTrendIcon = (change)=>{\n        if (change > 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-4 w-4 text-green-600\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 28\n        }, this);\n        if (change < 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-4 w-4 text-red-600\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 28\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 12\n        }, this);\n    };\n    const getPriorityBadge = (priority)=>{\n        switch(priority){\n            case 'critical':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"Critical\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 16\n                }, this);\n            case 'high':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    className: \"bg-orange-100 text-orange-800\",\n                    children: \"High\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 16\n                }, this);\n            case 'medium':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"Medium\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Low\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                className: \"h-8 w-64\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                className: \"h-4 w-96\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: Array.from({\n                                length: 4\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                className: \"h-12 w-12 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                className: \"h-8 w-16 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                className: \"h-4 w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this)\n                                }, i, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 30\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 161,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Warranty Status Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Monitor warranty status and expiration alerts across all products\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"secondary\",\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(isRefreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"secondary\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                        href: \"/warranties/alerts\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View Alerts\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                    className: \"text-black\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 21\n                        }, this),\n                        statusData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Total Warranties\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: statusData.summary.total\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-8 w-8 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: statusData.summary.active\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                    children: [\n                                                                        getTrendIcon(statusData.trends.activeChange),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                Math.abs(statusData.trends.activeChange),\n                                                                                \" this month\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-8 w-8 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Expiring Soon\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: statusData.summary.expiring\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                    children: [\n                                                                        getTrendIcon(statusData.trends.expiringChange),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                Math.abs(statusData.trends.expiringChange),\n                                                                                \" this month\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-8 w-8 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Expired\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: statusData.summary.expired\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                    children: [\n                                                                        getTrendIcon(statusData.trends.expiredChange),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                Math.abs(statusData.trends.expiredChange),\n                                                                                \" this month\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-red-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-black\",\n                                                        children: \"Warranty Status Distribution\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Active\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    getStatusPercentage(statusData.summary.active, statusData.summary.total),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 295,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-green-600 h-2 rounded-full\",\n                                                                            style: {\n                                                                                width: \"\".concat(getStatusPercentage(statusData.summary.active, statusData.summary.total), \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Expiring Soon\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 308,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    getStatusPercentage(statusData.summary.expiring, statusData.summary.total),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 309,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-yellow-600 h-2 rounded-full\",\n                                                                            style: {\n                                                                                width: \"\".concat(getStatusPercentage(statusData.summary.expiring, statusData.summary.total), \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Expired\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 322,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    getStatusPercentage(statusData.summary.expired, statusData.summary.total),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 323,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-red-600 h-2 rounded-full\",\n                                                                            style: {\n                                                                                width: \"\".concat(getStatusPercentage(statusData.summary.expired, statusData.summary.total), \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-black\",\n                                                        children: \"Expiration Timeline\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-red-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-red-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Next 7 Days\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"destructive\",\n                                                                        children: statusData.expiringBreakdown.next7Days\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-yellow-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Next 30 Days\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"bg-yellow-100 text-yellow-800\",\n                                                                        children: statusData.expiringBreakdown.next30Days\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Next 90 Days\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"bg-blue-100 text-blue-800\",\n                                                                        children: statusData.expiringBreakdown.next90Days\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-black\",\n                                                    children: \"Recent Alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-black\",\n                                                    children: \"Latest warranty expiration notifications and alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: statusData.recentAlerts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-8 w-8 mx-auto text-gray-400 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"No recent alerts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 59\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: statusData.recentAlerts.map((alert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    alert.type === 'expiring' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 58\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 114\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: alert.message\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: alert.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            getPriorityBadge(alert.priority)\n                                                        ]\n                                                    }, alert.id, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 61\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 30\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n        lineNumber: 183,\n        columnNumber: 10\n    }, this);\n}\n_s(WarrantyStatusPage, \"z8S2GUJE7HR5JfwCno5EwJiU7G8=\");\n_c1 = WarrantyStatusPage;\n_s1(WarrantyStatusPage, \"z8S2GUJE7HR5JfwCno5EwJiU7G8=\");\n_c = WarrantyStatusPage;\nvar _c;\n$RefreshReg$(_c, \"WarrantyStatusPage\");\nvar _c1;\n$RefreshReg$(_c1, \"WarrantyStatusPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/status/page.tsx\n"));

/***/ })

});