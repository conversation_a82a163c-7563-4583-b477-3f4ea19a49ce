"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/new/page",{

/***/ "(app-pages-browser)/./src/app/warranties/new/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/warranties/new/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewWarrantyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_11__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * New Warranty Page\n *\n * This page provides a form for creating new warranties with support for\n * different warranty types (in-warranty, out-warranty, BLUESTAR-specific).\n */ function NewWarrantyPage() {\n    _s();\n    _s1();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const warrantyType = searchParams.get('type') || 'in-warranty';\n    const vendor = searchParams.get('vendor');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: '',\n        executiveId: '',\n        contactPersonId: '',\n        bslNo: '',\n        bslDate: '',\n        bslAmount: '',\n        frequency: '',\n        numberOfMachines: '1',\n        installDate: '',\n        warrantyDate: '',\n        warningDate: '',\n        technicianId: '',\n        amcId: '',\n        status: 'ACTIVE',\n        // BLUESTAR specific fields\n        bluestarWarrantyCode: '',\n        bluestarServiceCenter: '',\n        bluestarContactPerson: '',\n        bluestarContactPhone: '',\n        specialTerms: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('basic');\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [executives, setExecutives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingData, setIsLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Load customers and executives\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewWarrantyPage.useEffect\": ()=>{\n            const loadFormData = {\n                \"NewWarrantyPage.useEffect.loadFormData\": async ()=>{\n                    try {\n                        setIsLoadingData(true);\n                        const [customersRes, executivesRes] = await Promise.all([\n                            fetch('/api/customers?take=1000', {\n                                credentials: 'include'\n                            }),\n                            fetch('/api/users?role=EXECUTIVE&take=1000', {\n                                credentials: 'include'\n                            })\n                        ]);\n                        if (customersRes.ok) {\n                            const customersData = await customersRes.json();\n                            setCustomers(customersData.customers || []);\n                        }\n                        if (executivesRes.ok) {\n                            const executivesData = await executivesRes.json();\n                            setExecutives(executivesData.users || []);\n                        }\n                    } catch (error) {\n                        console.error('Error loading form data:', error);\n                    } finally{\n                        setIsLoadingData(false);\n                    }\n                }\n            }[\"NewWarrantyPage.useEffect.loadFormData\"];\n            loadFormData();\n        }\n    }[\"NewWarrantyPage.useEffect\"], []);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.customerId) {\n            newErrors.customerId = 'Customer is required';\n        }\n        if (!formData.bslNo) {\n            newErrors.bslNo = 'BSL Number is required';\n        }\n        if (!formData.numberOfMachines || parseInt(formData.numberOfMachines) < 1) {\n            newErrors.numberOfMachines = 'Number of machines must be at least 1';\n        }\n        if (formData.installDate && formData.warrantyDate) {\n            const installDate = new Date(formData.installDate);\n            const warrantyDate = new Date(formData.warrantyDate);\n            if (warrantyDate <= installDate) {\n                newErrors.warrantyDate = 'Warranty date must be after install date';\n            }\n        }\n        if (vendor === 'bluestar' && !formData.bluestarWarrantyCode) {\n            newErrors.bluestarWarrantyCode = 'BLUESTAR warranty code is required';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Prepare warranty data for API\n            const warrantyData = {\n                customerId: formData.customerId,\n                executiveId: formData.executiveId || null,\n                contactPersonId: formData.contactPersonId || null,\n                bslNo: formData.bslNo || null,\n                bslDate: formData.bslDate ? new Date(formData.bslDate).toISOString() : null,\n                bslAmount: formData.bslAmount ? parseFloat(formData.bslAmount) : 0,\n                frequency: formData.frequency ? parseInt(formData.frequency) : 0,\n                numberOfMachines: parseInt(formData.numberOfMachines),\n                installDate: formData.installDate ? new Date(formData.installDate).toISOString() : null,\n                warrantyDate: formData.warrantyDate ? new Date(formData.warrantyDate).toISOString() : null,\n                warningDate: formData.warningDate ? new Date(formData.warningDate).toISOString() : null,\n                technicianId: formData.technicianId || null,\n                amcId: formData.amcId || null,\n                status: formData.status,\n                // Add BLUESTAR specific data if applicable\n                ...vendor === 'bluestar' && {\n                    vendorSpecific: {\n                        bluestarWarrantyCode: formData.bluestarWarrantyCode,\n                        bluestarServiceCenter: formData.bluestarServiceCenter,\n                        bluestarContactPerson: formData.bluestarContactPerson,\n                        bluestarContactPhone: formData.bluestarContactPhone,\n                        specialTerms: formData.specialTerms\n                    }\n                }\n            };\n            const response = await fetch('/api/warranties', {\n                method: 'POST',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(warrantyData)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        error: 'Unknown error'\n                    }));\n                console.error('Warranty creation error:', response.status, response.statusText, errorData);\n                if (response.status === 401) {\n                    throw new Error('Authentication required. Please log in.');\n                } else if (response.status === 400) {\n                    // Handle validation errors\n                    if (errorData.details && Array.isArray(errorData.details)) {\n                        const validationErrors = errorData.details.map((err)=>err.message).join(', ');\n                        throw new Error(\"Validation error: \".concat(validationErrors));\n                    }\n                    throw new Error(errorData.error || 'Invalid data provided');\n                } else if (response.status === 409) {\n                    throw new Error(errorData.error || 'Warranty with this BSL number already exists');\n                } else {\n                    throw new Error(errorData.error || \"Server error: \".concat(response.statusText));\n                }\n            }\n            const createdWarranty = await response.json();\n            // Redirect to appropriate page after creation\n            if (warrantyType === 'out-warranty') {\n                window.location.href = '/warranties/out-warranty';\n            } else if (vendor === 'bluestar') {\n                window.location.href = '/warranties/bluestar';\n            } else {\n                window.location.href = '/warranties/in-warranty';\n            }\n        } catch (error) {\n            console.error('Error creating warranty:', error);\n            setErrors({\n                submit: error.message || 'Failed to create warranty. Please try again.'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getPageTitle = ()=>{\n        if (vendor === 'bluestar') return 'New BLUESTAR Warranty';\n        if (warrantyType === 'out-warranty') return 'New Out-of-Warranty';\n        return 'New In-Warranty';\n    };\n    const getBackUrl = ()=>{\n        if (vendor === 'bluestar') return '/warranties/bluestar';\n        if (warrantyType === 'out-warranty') return '/warranties/out-warranty';\n        return '/warranties/in-warranty';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: getPageTitle()\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Create a new warranty record with machine and component details\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"secondary\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                    href: getBackUrl(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Cancel\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                                defaultValue: \"basic\",\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"basic\",\n                                                children: \"Basic Information\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"dates\",\n                                                children: \"Dates & Terms\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"machines\",\n                                                children: \"Machines\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            vendor === 'bluestar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"bluestar\",\n                                                children: \"BLUESTAR Details\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"basic\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"customerId\",\n                                                            className: \"text-black\",\n                                                            children: \"Customer *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.customerId,\n                                                            onValueChange: (value)=>handleInputChange('customerId', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"customerId\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: isLoadingData ? \"Loading customers...\" : \"Select customer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: customer.id,\n                                                                                children: [\n                                                                                    customer.name,\n                                                                                    \" \",\n                                                                                    customer.city && \"(\".concat(customer.city, \")\")\n                                                                                ]\n                                                                            }, customer.id, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 250,\n                                                                                columnNumber: 52\n                                                                            }, this)),\n                                                                        customers.length === 0 && !isLoadingData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"no-customers\",\n                                                                            disabled: true,\n                                                                            children: \"No customers available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 70\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.customerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.customerId\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"executiveId\",\n                                                            className: \"text-black\",\n                                                            children: \"Executive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.executiveId,\n                                                            onValueChange: (value)=>handleInputChange('executiveId', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"executiveId\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: isLoadingData ? \"Loading executives...\" : \"Select executive\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        executives.map((executive)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: executive.id,\n                                                                                children: [\n                                                                                    executive.name,\n                                                                                    \" \",\n                                                                                    executive.email && \"(\".concat(executive.email, \")\")\n                                                                                ]\n                                                                            }, executive.id, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 54\n                                                                            }, this)),\n                                                                        executives.length === 0 && !isLoadingData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"no-executives\",\n                                                                            disabled: true,\n                                                                            children: \"No executives available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 71\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslNo\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Number *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bslNo\",\n                                                            value: formData.bslNo,\n                                                            onChange: (e)=>handleInputChange('bslNo', e.target.value),\n                                                            placeholder: \"Enter BSL number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.bslNo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.bslNo\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 38\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslAmount\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"bslAmount\",\n                                                                    type: \"number\",\n                                                                    value: formData.bslAmount,\n                                                                    onChange: (e)=>handleInputChange('bslAmount', e.target.value),\n                                                                    placeholder: \"0\",\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"numberOfMachines\",\n                                                            className: \"text-black\",\n                                                            children: \"Number of Machines *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"numberOfMachines\",\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            value: formData.numberOfMachines,\n                                                            onChange: (e)=>handleInputChange('numberOfMachines', e.target.value),\n                                                            placeholder: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.numberOfMachines && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.numberOfMachines\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            className: \"text-black\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.status,\n                                                            onValueChange: (value)=>handleInputChange('status', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"status\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"ACTIVE\",\n                                                                            children: \"Active\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"PENDING\",\n                                                                            children: \"Pending\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"EXPIRED\",\n                                                                            children: \"Expired\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"CANCELLED\",\n                                                                            children: \"Cancelled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"dates\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslDate\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"bslDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.bslDate,\n                                                                    onChange: (e)=>handleInputChange('bslDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"installDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Install Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"installDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.installDate,\n                                                                    onChange: (e)=>handleInputChange('installDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"warrantyDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Warranty Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"warrantyDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.warrantyDate,\n                                                                    onChange: (e)=>handleInputChange('warrantyDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.warrantyDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.warrantyDate\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"warningDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Warning Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"warningDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.warningDate,\n                                                                    onChange: (e)=>handleInputChange('warningDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"frequency\",\n                                                            className: \"text-black\",\n                                                            children: \"Frequency\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"frequency\",\n                                                            type: \"number\",\n                                                            value: formData.frequency,\n                                                            onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"machines\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-black mb-2\",\n                                                    children: \"Machine Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Machine and component assignment interface will be implemented here.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Add Machine\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    vendor === 'bluestar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"bluestar\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarWarrantyCode\",\n                                                            className: \"text-black\",\n                                                            children: \"BLUESTAR Warranty Code *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarWarrantyCode\",\n                                                            value: formData.bluestarWarrantyCode,\n                                                            onChange: (e)=>handleInputChange('bluestarWarrantyCode', e.target.value),\n                                                            placeholder: \"Enter BLUESTAR warranty code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        errors.bluestarWarrantyCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.bluestarWarrantyCode\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarServiceCenter\",\n                                                            className: \"text-black\",\n                                                            children: \"Service Center\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.bluestarServiceCenter,\n                                                            onValueChange: (value)=>handleInputChange('bluestarServiceCenter', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"bluestarServiceCenter\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select service center\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Mumbai Central Service Center\",\n                                                                            children: \"Mumbai Central\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Delhi North Service Center\",\n                                                                            children: \"Delhi North\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Bangalore South Service Center\",\n                                                                            children: \"Bangalore South\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 388,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Chennai East Service Center\",\n                                                                            children: \"Chennai East\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Kolkata West Service Center\",\n                                                                            children: \"Kolkata West\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Pune Central Service Center\",\n                                                                            children: \"Pune Central\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarContactPerson\",\n                                                            className: \"text-black\",\n                                                            children: \"Contact Person\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarContactPerson\",\n                                                            value: formData.bluestarContactPerson,\n                                                            onChange: (e)=>handleInputChange('bluestarContactPerson', e.target.value),\n                                                            placeholder: \"Enter contact person name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarContactPhone\",\n                                                            className: \"text-black\",\n                                                            children: \"Contact Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarContactPhone\",\n                                                            value: formData.bluestarContactPhone,\n                                                            onChange: (e)=>handleInputChange('bluestarContactPhone', e.target.value),\n                                                            placeholder: \"Enter contact phone number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"specialTerms\",\n                                                            className: \"text-black\",\n                                                            children: \"Special Terms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                            id: \"specialTerms\",\n                                                            value: formData.specialTerms,\n                                                            onChange: (e)=>handleInputChange('specialTerms', e.target.value),\n                                                            placeholder: \"Enter any special terms or conditions\",\n                                                            rows: 3\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 41\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                        className: \"text-black\",\n                                        children: errors.submit\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                            href: getBackUrl(),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Creating...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Create Warranty\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 10\n    }, this);\n}\n_s(NewWarrantyPage, \"oEA5x5Ns+OO7pzg4GKVLkkF1wtI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = NewWarrantyPage;\n_s1(NewWarrantyPage, \"oEA5x5Ns+OO7pzg4GKVLkkF1wtI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = NewWarrantyPage;\nvar _c;\n$RefreshReg$(_c, \"NewWarrantyPage\");\nvar _c1;\n$RefreshReg$(_c1, \"NewWarrantyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/new/page.tsx\n"));

/***/ })

});