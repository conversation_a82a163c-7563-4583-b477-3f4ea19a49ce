'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  Search, 
  Filter, 
  Download, 
  Plus, 
  Edit, 
  Trash2, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Building
} from 'lucide-react';

import { format } from 'date-fns';

interface ServiceDate {
  id: string;
  amcContractId: string;
  serviceDate: string;
  serviceType: string;
  status: 'SCHEDULED' | 'COMPLETED' | 'CANCELLED' | 'OVERDUE';
  notes?: string;
  technicianId?: string;
  completedAt?: string;
  amcContract: {
    id: string;
    contractNumber: string;
    customer: {
      id: string;
      name: string;
      city: string;
    };
  };
  technician?: {
    id: string;
    name: string;
  };
}

export default function ServiceDatesPage() {
  const [serviceDates, setServiceDates] = useState<ServiceDate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [serviceTypeFilter, setServiceTypeFilter] = useState('all');

  // Load service dates
  useEffect(() => {
    const loadServiceDates = async () => {
      try {
        setIsLoading(true);
        
        const params = new URLSearchParams();
        if (searchTerm) params.set('search', searchTerm);
        if (statusFilter !== 'all') params.set('status', statusFilter);
        if (serviceTypeFilter !== 'all') params.set('serviceType', serviceTypeFilter);
        
        const response = await fetch(`/api/amc/service-dates?${params}`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (!response.ok) {
          throw new Error('Failed to load service dates');
        }
        
        const data = await response.json();
        setServiceDates(data.serviceDates || []);
        setError(null);
      } catch (err: any) {
        console.error('Error loading service dates:', err);
        setError(err.message || 'Failed to load service dates');
        setServiceDates([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadServiceDates();
  }, [searchTerm, statusFilter, serviceTypeFilter]);

  const handleExport = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.set('search', searchTerm);
      if (statusFilter !== 'all') params.set('status', statusFilter);
      if (serviceTypeFilter !== 'all') params.set('serviceType', serviceTypeFilter);
      params.set('format', 'CSV');
      
      const response = await fetch(`/api/amc/service-dates/export?${params}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to export service dates');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `service-dates-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting service dates:', error);
      setError('Failed to export service dates');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4" />;
      case 'SCHEDULED':
        return <Clock className="h-4 w-4" />;
      case 'OVERDUE':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'SCHEDULED':
        return 'bg-blue-100 text-blue-800';
      case 'OVERDUE':
        return 'bg-red-100 text-red-800';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
        {/* Header Card */}
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Service Dates</span>
                </CardTitle>
                <CardDescription className="text-gray-100">
                  Manage and track AMC service schedules and completion dates
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="space-y-2">
                <Label htmlFor="search" className="text-black">Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Search contracts, customers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status" className="text-black">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                    <SelectItem value="COMPLETED">Completed</SelectItem>
                    <SelectItem value="OVERDUE">Overdue</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="serviceType" className="text-black">Service Type</Label>
                <Select value={serviceTypeFilter} onValueChange={setServiceTypeFilter}>
                  <SelectTrigger id="serviceType">
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="PREVENTIVE">Preventive</SelectItem>
                    <SelectItem value="CORRECTIVE">Corrective</SelectItem>
                    <SelectItem value="EMERGENCY">Emergency</SelectItem>
                    <SelectItem value="INSPECTION">Inspection</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-black">Actions</Label>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    More Filters
                  </Button>
                </div>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <Alert className="mb-6">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-black">{error}</AlertDescription>
              </Alert>
            )}

            {/* Service Dates Table */}
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : serviceDates.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-black mb-2">No Service Dates Found</h3>
                    <p className="text-gray-600 mb-4">
                      {searchTerm || statusFilter !== 'all' || serviceTypeFilter !== 'all'
                        ? 'No service dates match your current filters.'
                        : 'No service dates have been scheduled yet.'}
                    </p>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Schedule First Service
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-black">Service Date</TableHead>
                      <TableHead className="text-black">Contract</TableHead>
                      <TableHead className="text-black">Customer</TableHead>
                      <TableHead className="text-black">Service Type</TableHead>
                      <TableHead className="text-black">Status</TableHead>
                      <TableHead className="text-black">Technician</TableHead>
                      <TableHead className="text-black">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {serviceDates.map((serviceDate) => (
                      <TableRow key={serviceDate.id}>
                        <TableCell className="text-black">
                          <div>
                            <div className="font-medium">
                              {format(new Date(serviceDate.serviceDate), 'MMM dd, yyyy')}
                            </div>
                            <div className="text-sm text-gray-500">
                              {format(new Date(serviceDate.serviceDate), 'h:mm a')}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-black">
                          <div>
                            <div className="font-medium">{serviceDate.amcContract.contractNumber}</div>
                          </div>
                        </TableCell>
                        <TableCell className="text-black">
                          <div>
                            <div className="font-medium">{serviceDate.amcContract.customer.name}</div>
                            <div className="text-sm text-gray-500">{serviceDate.amcContract.customer.city}</div>
                          </div>
                        </TableCell>
                        <TableCell className="text-black">
                          <Badge variant="outline">{serviceDate.serviceType}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={`${getStatusColor(serviceDate.status)} flex items-center space-x-1 w-fit`}>
                            {getStatusIcon(serviceDate.status)}
                            <span>{serviceDate.status}</span>
                          </Badge>
                        </TableCell>
                        <TableCell className="text-black">
                          {serviceDate.technician?.name || 'Not assigned'}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
    </div>
  );
}
